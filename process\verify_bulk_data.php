<?php
session_start();

// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require __DIR__ . '/../db_connect.php';
require __DIR__ . '/../vendor/autoload.php';

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.close();</script>");
}

$username = $_SESSION['username'];
error_log("Verification started for user: " . $username);

// Check if this is a verification request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'verify' && isset($_FILES['excel_file'])) {
    
    // Validate file upload
    if ($_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
        die("<script>alert('File upload error.'); window.close();</script>");
    }

    $allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    if (!in_array($_FILES['excel_file']['type'], $allowedTypes)) {
        die("<script>alert('Invalid file type. Please upload an Excel file.'); window.close();</script>");
    }

    try {
        $file = $_FILES['excel_file']['tmp_name'];
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file);
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        // Get C-Note validation setting
        $settings_stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
        $settings_stmt->bind_param("s", $username);
        $settings_stmt->execute();
        $settings_result = $settings_stmt->get_result();
        $settings_data = $settings_result->fetch_assoc();
        $cnote_validation_enabled = $settings_data['cnote_validation_enabled'] ?? 1;
        $settings_stmt->close();

        // Valid mode of transport options
        $valid_modes = ['Express', 'Surface', 'Air Cargo', 'Premium', 'PTP', 'COD', 'International', 'E-Com Express', 'E-Com Surface'];

        $verification_results = [];
        $total_rows = 0;
        $valid_rows = 0;
        $error_rows = 0;
        $docket_numbers = []; // Track docket numbers for duplicate detection

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // Skip header row

            $total_rows++;
            $row_number = $total_rows; // Use total_rows counter for correct numbering starting from 1
            $errors = [];
            
            // Extract data from row with safe array access (12 columns format)
            $entry_type = $row[0] ?? '';
            $customer = $row[1] ?? '';
            $docket_no = $row[2] ?? '';
            $docket_date = $row[3] ?? '';
            $pincode = $row[4] ?? '';
            $destination = $row[5] ?? '';
            $weight = $row[6] ?? '';
            $mode_of_tsp = $row[7] ?? '';
            $waybill_value = $row[8] ?? '';
            $remarks = $row[9] ?? '';
            $username_col = $row[10] ?? '';
            $risk_type = $row[11] ?? '';

            // Use username from file or current user
            $file_username = !empty($username_col) ? $username_col : $username;

            // Validation 1: Customer Name with customers table
            if (!empty($customer)) {
                $customer_stmt = $conn->prepare("SELECT short_name FROM customers WHERE short_name = ? AND username = ?");
                $customer_stmt->bind_param("ss", $customer, $username);
                $customer_stmt->execute();
                $customer_result = $customer_stmt->get_result();
                if ($customer_result->num_rows === 0) {
                    $errors[] = "Customer '$customer' not found in customers table";
                }
                $customer_stmt->close();
            } else {
                $errors[] = "Customer name is required";
            }

            // Validation 2: Docket number validation (only if C-Note validation is enabled)
            if ($cnote_validation_enabled && !empty($docket_no)) {
                // Check if docket exists in cn_entries
                $cn_stmt = $conn->prepare("SELECT cn_number FROM cn_entries WHERE cn_number = ? AND username = ?");
                $cn_stmt->bind_param("ss", $docket_no, $username);
                $cn_stmt->execute();
                $cn_result = $cn_stmt->get_result();
                if ($cn_result->num_rows === 0) {
                    $errors[] = "Docket number '$docket_no' not found in CN entries";
                } else {
                    // Check if docket is already used in transactions
                    $trans_stmt = $conn->prepare("SELECT docket_no FROM transactions WHERE docket_no = ?");
                    $trans_stmt->bind_param("s", $docket_no);
                    $trans_stmt->execute();
                    $trans_result = $trans_stmt->get_result();
                    if ($trans_result->num_rows > 0) {
                        $errors[] = "Docket number '$docket_no' is already used in transactions";
                    }
                    $trans_stmt->close();
                }
                $cn_stmt->close();
            } elseif (empty($docket_no)) {
                $errors[] = "Docket number is required";
            }

            // Validation 3: Pincode with pincode_data table
            if (!empty($pincode)) {
                $pincode_stmt = $conn->prepare("SELECT pincode FROM pincode_data WHERE pincode = ?");
                $pincode_stmt->bind_param("s", $pincode);
                $pincode_stmt->execute();
                $pincode_result = $pincode_stmt->get_result();
                if ($pincode_result->num_rows === 0) {
                    $errors[] = "Pincode '$pincode' not found in pincode data";
                }
                $pincode_stmt->close();
            } else {
                $errors[] = "Pincode is required";
            }

            // Validation 4: Mode of transport
            if (!empty($mode_of_tsp)) {
                if (!in_array($mode_of_tsp, $valid_modes)) {
                    $errors[] = "Invalid mode of transport '$mode_of_tsp'. Valid options: " . implode(', ', $valid_modes);
                }
            } else {
                $errors[] = "Mode of transport is required";
            }

            // Validation 5: Username verification
            if (!empty($file_username)) {
                if ($file_username !== $username) {
                    $errors[] = "Username '$file_username' does not match current user '$username'";
                }
            } else {
                $errors[] = "Username is required";
            }

            // Validation 6: Check for duplicate docket numbers within the file
            if (!empty($docket_no)) {
                if (isset($docket_numbers[$docket_no])) {
                    // Current row has duplicate
                    $errors[] = "Duplicate docket number '$docket_no' found (also in row " . $docket_numbers[$docket_no] . ")";

                    // Mark the original row as having a duplicate too
                    $original_row_index = $docket_numbers[$docket_no] - 1; // Convert to 0-based index for results array

                    // Find and update the original row in verification_results
                    foreach ($verification_results as &$result) {
                        if ($result['row'] == $docket_numbers[$docket_no]) {
                            // Add duplicate error to the original row
                            if ($result['status'] === 'success') {
                                // Convert from success to error
                                $result['status'] = 'error';
                                $result['errors'] = ["Duplicate docket number '$docket_no' found (also in row $row_number)"];
                                unset($result['message']);

                                // Update counters
                                $valid_rows--;
                                $error_rows++;
                            } else {
                                // Add to existing errors
                                $result['errors'][] = "Duplicate docket number '$docket_no' found (also in row $row_number)";
                            }
                            break;
                        }
                    }
                } else {
                    $docket_numbers[$docket_no] = $row_number; // Store row number for this docket
                }
            }

            // Additional basic validations
            if (empty($weight) || !is_numeric($weight)) {
                $errors[] = "Valid weight is required";
            }
            // Waybill value is optional - only validate if provided
            if (!empty($waybill_value) && !is_numeric($waybill_value)) {
                $errors[] = "Waybill value must be numeric if provided";
            }
            if (empty($docket_date)) {
                $errors[] = "Docket date is required";
            }
            if (empty($destination)) {
                $errors[] = "Destination is required";
            }

            // Store results
            if (empty($errors)) {
                $valid_rows++;
                $verification_results[] = [
                    'row' => $row_number,
                    'status' => 'success',
                    'message' => 'All validations passed',
                    'data' => $row
                ];
            } else {
                $error_rows++;
                $verification_results[] = [
                    'row' => $row_number,
                    'status' => 'error',
                    'errors' => $errors,
                    'data' => $row
                ];
            }
        }

        // Store results and file data in session for the results page
        $_SESSION['verification_results'] = [
            'total_rows' => $total_rows,
            'valid_rows' => $valid_rows,
            'error_rows' => $error_rows,
            'results' => $verification_results,
            'cnote_validation_enabled' => $cnote_validation_enabled,
            'file_data' => $rows, // Store the Excel data for upload
            'file_name' => $_FILES['excel_file']['name']
        ];

        // If all validations passed, set a flag for the original upload process
        if ($error_rows === 0) {
            $_SESSION['verification_passed'] = true;
            $_SESSION['verified_file_name'] = $_FILES['excel_file']['name'];
        }

        // Redirect to results page
        header('Location: verification_results.php');
        exit();

    } catch (Exception $e) {
        die("<script>alert('Error processing file: " . addslashes($e->getMessage()) . "'); window.close();</script>");
    }
} else {
    die("<script>alert('Invalid request.'); window.close();</script>");
}

$conn->close();
?>
