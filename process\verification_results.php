<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.close();</script>");
}

// Check if verification results exist
if (!isset($_SESSION['verification_results'])) {
    die("<script>alert('No verification results found.'); window.close();</script>");
}

$results = $_SESSION['verification_results'];
$total_rows = $results['total_rows'];
$valid_rows = $results['valid_rows'];
$error_rows = $results['error_rows'];
$verification_results = $results['results'];
$cnote_validation_enabled = $results['cnote_validation_enabled'];
$file_data = $results['file_data'] ?? null;
$file_name = $results['file_name'] ?? 'uploaded_file.xlsx';

// Store export data in session for Excel download
$_SESSION['verification_export_data'] = [
    'results' => $verification_results,
    'total_rows' => $total_rows,
    'valid_rows' => $valid_rows,
    'error_rows' => $error_rows,
    'cnote_validation_enabled' => $cnote_validation_enabled
];

// Don't clear session data yet - we might need it for upload
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Data Verification Results</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        .total { background-color: #3498db; }
        .valid { background-color: #27ae60; }
        .error { background-color: #e74c3c; }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .results-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .success-row {
            background-color: #d4edda;
        }
        .error-row {
            background-color: #f8d7da;
        }
        .error-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .error-list li {
            padding: 2px 0;
            color: #721c24;
        }
        .error-list li:before {
            content: "• ";
            color: #dc3545;
            font-weight: bold;
        }
        .success-message {
            color: #155724;
            font-weight: bold;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .validation-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .data-preview {
            font-size: 12px;
            color: #666;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            animation: slideIn 0.3s;
        }

        .modal-header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2em;
        }

        .modal-body {
            padding: 20px;
            text-align: center;
        }

        .modal-body p {
            margin: 10px 0;
            font-size: 1.1em;
        }

        .validation-summary {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            text-align: left;
        }

        .validation-summary ul {
            margin: 0;
            padding-left: 20px;
        }

        .validation-summary li {
            margin: 5px 0;
            color: #28a745;
        }

        .modal-footer {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Bulk Data Verification Results</h1>
            <p>Verification completed for Excel file upload</p>
        </div>

        <?php if (!$cnote_validation_enabled): ?>
        <div class="validation-note">
            <strong>Note:</strong> C-Note validation is currently disabled in your settings. Docket number validation against CN entries table is skipped.
        </div>
        <?php endif; ?>

        <div class="summary">
            <div class="summary-card total">
                <h3><?php echo $total_rows; ?></h3>
                <p>Total Rows</p>
            </div>
            <div class="summary-card valid">
                <h3><?php echo $valid_rows; ?></h3>
                <p>Valid Rows</p>
            </div>
            <div class="summary-card error">
                <h3><?php echo $error_rows; ?></h3>
                <p>Rows with Errors</p>
            </div>
        </div>

        <?php if ($error_rows > 0): ?>
        <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <strong>⚠️ Validation Issues Found:</strong> Please resolve the errors below before uploading the data.
        </div>
        <?php else: ?>
        <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <strong>✅ All Data Valid:</strong> Your Excel file passed all validation checks and is ready for upload.
        </div>
        <?php endif; ?>

        <div class="actions" style="margin-bottom: 30px;">
            <button class="btn btn-secondary" onclick="downloadAsExcel()">Download as Excel</button>
            <button class="btn btn-secondary" onclick="window.print()">Print Results</button>
            <button class="btn btn-secondary" onclick="window.close()">Close Window</button>
            <?php if ($error_rows === 0): ?>
            <button class="btn btn-primary" onclick="showUploadModal()">Proceed to Upload</button>
            <?php endif; ?>
        </div>

        <table class="results-table">
            <thead>
                <tr>
                    <th>Row #</th>
                    <th>Status</th>
                    <th>Customer</th>
                    <th>Docket No</th>
                    <th>Pincode</th>
                    <th>Mode</th>
                    <th>Risk Type</th>
                    <th>Issues/Remarks</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($verification_results as $result): ?>
                <tr class="<?php echo $result['status'] === 'success' ? 'success-row' : 'error-row'; ?>">
                    <td><?php echo $result['row']; ?></td>
                    <td>
                        <?php if ($result['status'] === 'success'): ?>
                            <span style="color: #27ae60;">✅ Valid</span>
                        <?php else: ?>
                            <span style="color: #e74c3c;">❌ Error</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo htmlspecialchars($result['data'][1] ?? ''); ?></td>
                    <td><?php echo htmlspecialchars($result['data'][2] ?? ''); ?></td>
                    <td><?php echo htmlspecialchars($result['data'][4] ?? ''); ?></td>
                    <td><?php echo htmlspecialchars($result['data'][7] ?? ''); ?></td>
                    <td><?php echo htmlspecialchars($result['data'][11] ?? ''); ?></td>
                    <td>
                        <?php if ($result['status'] === 'success'): ?>
                            <span class="success-message"><?php echo $result['message']; ?></span>
                        <?php else: ?>
                            <ul class="error-list">
                                <?php foreach ($result['errors'] as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>


    </div>

    <!-- Upload Confirmation Modal -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✅ Validation Successful</h3>
            </div>
            <div class="modal-body">
                <p><strong>All <?php echo $total_rows; ?> rows passed validation!</strong></p>
                <p>Would you like to upload this file now?</p>
                <div class="validation-summary">
                    <ul>
                        <li>✅ Customer names verified</li>
                        <li>✅ <?php echo $cnote_validation_enabled ? 'Docket numbers verified' : 'Docket validation skipped (disabled)'; ?></li>
                        <li>✅ Pincodes verified</li>
                        <li>✅ Transport modes verified</li>
                        <li>✅ All required fields validated</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button class="btn btn-primary" onclick="initiateUpload()">Yes, Upload Now</button>
            </div>
        </div>
    </div>

    <script>
        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('uploadModal').style.display = 'none';
        }

        function initiateUpload() {
            // Create a form to submit directly to the original process_credit_entry.php
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '../process/process_credit_entry.php';
            form.enctype = 'multipart/form-data';
            form.style.display = 'none';

            // Add a hidden input to indicate this is from verification
            const verifiedInput = document.createElement('input');
            verifiedInput.type = 'hidden';
            verifiedInput.name = 'from_verification';
            verifiedInput.value = 'true';
            form.appendChild(verifiedInput);

            // Submit the form
            document.body.appendChild(form);
            form.submit();
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('uploadModal');
            if (event.target == modal) {
                closeModal();
            }
        }

        function downloadAsExcel() {
            // Use window.location for reliable file download
            window.location.href = 'export_verification_excel.php';
        }

        // Auto-show modal if all validations passed
        <?php if ($error_rows === 0): ?>
        window.onload = function() {
            setTimeout(showUploadModal, 1000); // Show modal after 1 second
        };
        <?php endif; ?>
    </script>
</body>
</html>
