<?php
// Ensure no output before headers
ob_start();

session_start();

// Debug mode - if debug parameter is passed, show debug info instead of downloading
if (isset($_GET['debug'])) {
    ob_end_clean();
    echo "<h3>Debug Information</h3>";
    echo "Session ID: " . session_id() . "<br>";
    echo "Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";
    echo "Verification data exists: " . (isset($_SESSION['verification_export_data']) ? 'Yes' : 'No') . "<br>";
    if (isset($_SESSION['verification_export_data'])) {
        echo "Total rows: " . $_SESSION['verification_export_data']['total_rows'] . "<br>";
        echo "Results count: " . count($_SESSION['verification_export_data']['results']) . "<br>";
    }
    echo "<br><a href='export_verification_excel.php'>Try Excel Export</a>";
    echo "<br><a href='verification_results.php'>Back to Results</a>";
    exit;
}

require __DIR__ . '/../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    ob_end_clean();
    die("Error: You must be logged in.");
}

// Check if verification results exist in session
if (!isset($_SESSION['verification_export_data'])) {
    ob_end_clean();
    error_log("Excel Export Error: No verification data found in session for user: " . ($_SESSION['username'] ?? 'unknown'));
    header('Content-Type: text/plain');
    die("Error: No verification data found for export. Please run verification again.");
}

$export_data = $_SESSION['verification_export_data'];
$verification_results = $export_data['results'];
$total_rows = $export_data['total_rows'];
$valid_rows = $export_data['valid_rows'];
$error_rows = $export_data['error_rows'];
$cnote_validation_enabled = $export_data['cnote_validation_enabled'];

// Don't clear the export data immediately - keep it for potential re-downloads
// unset($_SESSION['verification_export_data']);

try {
    // Create new Spreadsheet object
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Set document properties
    $spreadsheet->getProperties()
        ->setCreator("ALM System")
        ->setTitle("Bulk Data Verification Results")
        ->setSubject("Verification Results")
        ->setDescription("Verification results for bulk data upload");
    
    // Add summary information
    $sheet->setCellValue('A1', 'Bulk Data Verification Results');
    $sheet->setCellValue('A2', 'Generated on: ' . date('Y-m-d H:i:s'));
    $sheet->setCellValue('A3', 'Total Rows: ' . $total_rows);
    $sheet->setCellValue('A4', 'Valid Rows: ' . $valid_rows);
    $sheet->setCellValue('A5', 'Error Rows: ' . $error_rows);
    $sheet->setCellValue('A6', 'C-Note Validation: ' . ($cnote_validation_enabled ? 'Enabled' : 'Disabled'));
    
    // Style the header
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
    $sheet->getStyle('A2:A6')->getFont()->setBold(true);
    
    // Add table headers starting from row 8
    $headerRow = 8;
    $headers = ['Row #', 'Status', 'Customer', 'Docket No', 'Docket Date', 'Pincode', 'Destination', 'Weight', 'Mode', 'Waybill Value', 'Remarks', 'Username', 'Risk Type', 'Issues/Comments'];
    
    $col = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($col . $headerRow, $header);
        $sheet->getStyle($col . $headerRow)->getFont()->setBold(true);
        $sheet->getStyle($col . $headerRow)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFE0E0E0');
        $col++;
    }
    
    // Add data rows
    $currentRow = $headerRow + 1;
    foreach ($verification_results as $result) {
        $data = $result['data'];
        
        // Row number
        $sheet->setCellValue('A' . $currentRow, $result['row']);
        
        // Status
        $status = $result['status'] === 'success' ? 'Valid' : 'Error';
        $sheet->setCellValue('B' . $currentRow, $status);
        
        // Color code the status
        if ($result['status'] === 'success') {
            $sheet->getStyle('B' . $currentRow)->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFD4EDDA'); // Light green
        } else {
            $sheet->getStyle('B' . $currentRow)->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFF8D7DA'); // Light red
        }
        
        // Data columns
        $sheet->setCellValue('C' . $currentRow, $data[1] ?? ''); // Customer
        $sheet->setCellValue('D' . $currentRow, $data[2] ?? ''); // Docket No

        // Format docket date as dd-mm-yyyy
        $docketDate = $data[3] ?? '';
        if (!empty($docketDate)) {
            // Handle different date formats that might come from Excel
            if (is_numeric($docketDate)) {
                // Excel serial date number
                $unix_date = ($docketDate - 25569) * 86400;
                $formattedDate = date('d-m-Y', $unix_date);
            } else {
                // String date - try to parse and reformat
                $timestamp = strtotime($docketDate);
                if ($timestamp !== false) {
                    $formattedDate = date('d-m-Y', $timestamp);
                } else {
                    $formattedDate = $docketDate; // Keep original if can't parse
                }
            }
            $sheet->setCellValue('E' . $currentRow, $formattedDate);
        } else {
            $sheet->setCellValue('E' . $currentRow, '');
        }

        $sheet->setCellValue('F' . $currentRow, $data[4] ?? ''); // Pincode
        $sheet->setCellValue('G' . $currentRow, $data[5] ?? ''); // Destination
        $sheet->setCellValue('H' . $currentRow, $data[6] ?? ''); // Weight
        $sheet->setCellValue('I' . $currentRow, $data[7] ?? ''); // Mode
        $sheet->setCellValue('J' . $currentRow, $data[8] ?? ''); // Waybill Value
        $sheet->setCellValue('K' . $currentRow, $data[9] ?? ''); // Remarks
        $sheet->setCellValue('L' . $currentRow, $data[10] ?? ''); // Username
        $sheet->setCellValue('M' . $currentRow, $data[11] ?? ''); // Risk Type

        // Issues/Comments
        if ($result['status'] === 'success') {
            $sheet->setCellValue('N' . $currentRow, $result['message']);
        } else {
            $issues = implode('; ', $result['errors']);
            $sheet->setCellValue('N' . $currentRow, $issues);
        }
        
        $currentRow++;
    }
    
    // Auto-size columns
    foreach (range('A', 'N') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Set the active sheet index to the first sheet
    $spreadsheet->setActiveSheetIndex(0);
    
    // Generate filename
    $filename = 'verification_results_' . date('Y-m-d_H-i-s') . '.xlsx';

    // Clear any output buffer and set headers for download
    ob_end_clean();

    // Set headers for Excel download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Transfer-Encoding: binary');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    header('Expires: 0');

    // Create Excel writer and output
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit();

} catch (Exception $e) {
    ob_end_clean();
    header('Content-Type: text/plain');
    die('Error creating Excel file: ' . $e->getMessage());
}
?>
