<?php
// Simple test to check if Excel export is working
session_start();

// Check if we have session data
if (isset($_SESSION['verification_export_data'])) {
    echo "Session data exists:<br>";
    echo "Total rows: " . $_SESSION['verification_export_data']['total_rows'] . "<br>";
    echo "Valid rows: " . $_SESSION['verification_export_data']['valid_rows'] . "<br>";
    echo "Error rows: " . $_SESSION['verification_export_data']['error_rows'] . "<br>";
    echo "Results count: " . count($_SESSION['verification_export_data']['results']) . "<br>";
    echo "<br><a href='export_verification_excel.php' target='_blank'>Test Excel Export</a>";
} else {
    echo "No session data found. Please run verification first.";
}

echo "<br><br><a href='../bulk_entry.php'>Back to Bulk Entry</a>";
?>
